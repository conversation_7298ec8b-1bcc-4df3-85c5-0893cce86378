/**
 * Storage System Test Script
 * Tests both S3 and local storage functionality
 */

const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Import our storage utilities
const { validateStorageConfig, getStorageConfig } = require('../utils/storageConfig');
const { hasAWSCredentials, isS3Url } = require('../utils/storageHelper');

/**
 * Test storage configuration
 */
async function testStorageConfig() {
  console.log('\n=== Testing Storage Configuration ===');
  
  const validation = validateStorageConfig();
  
  console.log('Configuration Valid:', validation.isValid);
  console.log('Storage Config:', validation.config);
  
  if (validation.errors.length > 0) {
    console.log('\nErrors:');
    validation.errors.forEach(error => console.log(`  ❌ ${error}`));
  }
  
  if (validation.warnings.length > 0) {
    console.log('\nWarnings:');
    validation.warnings.forEach(warning => console.log(`  ⚠️  ${warning}`));
  }
  
  return validation;
}

/**
 * Test local storage setup
 */
async function testLocalStorage() {
  console.log('\n=== Testing Local Storage ===');
  
  const uploadsDir = path.join(process.cwd(), 'uploads');
  const testDirs = [
    'uploads',
    'uploads/profiles',
    'uploads/thumbnails',
    'uploads/content'
  ];
  
  console.log('Checking directories...');
  
  for (const dir of testDirs) {
    const fullPath = path.join(process.cwd(), dir);
    const exists = fs.existsSync(fullPath);
    
    if (!exists) {
      try {
        fs.mkdirSync(fullPath, { recursive: true });
        console.log(`  ✅ Created: ${dir}`);
      } catch (error) {
        console.log(`  ❌ Failed to create: ${dir} - ${error.message}`);
        return false;
      }
    } else {
      console.log(`  ✅ Exists: ${dir}`);
    }
  }
  
  // Test write permissions
  const testFile = path.join(uploadsDir, 'test-write.txt');
  try {
    fs.writeFileSync(testFile, 'test content');
    fs.unlinkSync(testFile);
    console.log('  ✅ Write permissions: OK');
    return true;
  } catch (error) {
    console.log(`  ❌ Write permissions: FAILED - ${error.message}`);
    return false;
  }
}

/**
 * Test S3 configuration
 */
async function testS3Config() {
  console.log('\n=== Testing S3 Configuration ===');
  
  const hasCredentials = hasAWSCredentials();
  console.log('AWS Credentials Available:', hasCredentials);
  
  if (!hasCredentials) {
    console.log('  ⚠️  S3 not configured - using local storage only');
    return false;
  }
  
  const requiredVars = [
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY', 
    'AWS_BUCKET_NAME',
    'AWS_REGION'
  ];
  
  console.log('Checking environment variables...');
  for (const varName of requiredVars) {
    const value = process.env[varName];
    if (value) {
      console.log(`  ✅ ${varName}: Set`);
    } else {
      console.log(`  ❌ ${varName}: Missing`);
    }
  }
  
  return hasCredentials;
}

/**
 * Test URL detection and handling
 */
async function testUrlHandling() {
  console.log('\n=== Testing URL Handling ===');
  
  const testUrls = [
    // S3 URLs
    'https://mybucket.s3.us-east-1.amazonaws.com/uploads/file.jpg',
    'https://s3.us-east-1.amazonaws.com/mybucket/uploads/file.jpg',
    
    // Local URLs
    '/uploads/file.jpg',
    'uploads/file.jpg',
    '/uploads/profiles/profile.jpg',
    
    // External URLs
    'https://example.com/image.jpg',
    'http://localhost:5000/uploads/file.jpg'
  ];
  
  console.log('Testing URL detection...');
  
  for (const url of testUrls) {
    const isS3 = isS3Url(url);
    const type = isS3 ? 'S3' : 'Local/External';
    console.log(`  ${url} → ${type}`);
  }
}

/**
 * Test file path normalization
 */
async function testPathNormalization() {
  console.log('\n=== Testing Path Normalization ===');
  
  const testPaths = [
    '/uploads/file.jpg',
    'uploads/file.jpg',
    './uploads/file.jpg',
    'uploads\\file.jpg', // Windows path
    '/uploads/profiles/profile.jpg'
  ];
  
  console.log('Testing path normalization...');
  
  for (const filePath of testPaths) {
    // Simulate the normalization logic from proxy.js
    let normalizedPath = filePath;
    
    // Remove leading slash if present
    if (normalizedPath.startsWith('/')) {
      normalizedPath = normalizedPath.substring(1);
    }
    
    // Remove 'uploads/' prefix if already present
    if (normalizedPath.startsWith('uploads/')) {
      normalizedPath = normalizedPath.substring(8);
    }
    
    const fullPath = path.join(process.cwd(), 'uploads', normalizedPath);
    console.log(`  ${filePath} → ${normalizedPath} → ${fullPath}`);
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🧪 XO Sports Hub Storage System Tests');
  console.log('=====================================');
  
  try {
    // Test configuration
    const configResult = await testStorageConfig();
    
    // Test local storage
    const localResult = await testLocalStorage();
    
    // Test S3 configuration
    const s3Result = await testS3Config();
    
    // Test URL handling
    await testUrlHandling();
    
    // Test path normalization
    await testPathNormalization();
    
    // Summary
    console.log('\n=== Test Summary ===');
    console.log(`Configuration: ${configResult.isValid ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Local Storage: ${localResult ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`S3 Storage: ${s3Result ? '✅ CONFIGURED' : '⚠️  NOT CONFIGURED'}`);
    
    if (configResult.isValid && localResult) {
      console.log('\n🎉 Storage system is ready!');
      
      if (s3Result) {
        console.log('   - S3 storage available for content files');
        console.log('   - Local storage available for profile images');
      } else {
        console.log('   - Local storage only (S3 not configured)');
      }
    } else {
      console.log('\n❌ Storage system has issues that need to be resolved');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  testStorageConfig,
  testLocalStorage,
  testS3Config,
  testUrlHandling,
  testPathNormalization,
  runTests
};
