import React, { useState, useEffect } from 'react';
import { 
  getUniversalFileUrl, 
  detectStorageType, 
  getPlaceholderImage,
  isS3Url 
} from '../../utils/constants';

/**
 * Universal File Display Component
 * Handles both S3 and local file display with automatic fallback
 * Supports images, videos, and documents
 */
const UniversalFileDisplay = ({
  filePath,
  contentId = null,
  fileType = 'file', // 'file', 'thumbnail', 'preview', 'stream'
  displayType = 'image', // 'image', 'video', 'document'
  alt = 'File',
  className = '',
  style = {},
  placeholder = null,
  onError = null,
  onLoad = null,
  preferProxy = false,
  enableFallback = true,
  ...props
}) => {
  const [currentUrl, setCurrentUrl] = useState(null);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [storageType, setStorageType] = useState('unknown');

  // Initialize URL and storage type detection
  useEffect(() => {
    if (!filePath) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    const detectedStorageType = detectStorageType(filePath);
    setStorageType(detectedStorageType);

    // Get the appropriate URL based on storage type and preferences
    const url = getUniversalFileUrl(filePath, {
      preferProxy,
      enableFallback,
      contentId,
      fileType
    });

    setCurrentUrl(url);
    setHasError(false);
    setIsLoading(false);
  }, [filePath, contentId, fileType, preferProxy, enableFallback]);

  // Handle load error with fallback
  const handleError = (error) => {
    console.warn(`[UniversalFileDisplay] Error loading ${storageType} file:`, currentUrl, error);
    
    if (enableFallback && !hasError) {
      // Try fallback URL if available
      if (isS3Url(filePath) && !preferProxy) {
        // Try proxy URL as fallback for S3
        const fallbackUrl = getUniversalFileUrl(filePath, {
          preferProxy: true,
          contentId,
          fileType
        });
        
        if (fallbackUrl !== currentUrl) {
          console.log(`[UniversalFileDisplay] Trying proxy fallback:`, fallbackUrl);
          setCurrentUrl(fallbackUrl);
          return;
        }
      }
    }

    setHasError(true);
    if (onError) onError(error);
  };

  // Handle successful load
  const handleLoad = (event) => {
    setIsLoading(false);
    if (onLoad) onLoad(event);
  };

  // Get placeholder based on display type
  const getPlaceholder = () => {
    if (placeholder) return placeholder;
    
    switch (displayType) {
      case 'image':
        return getPlaceholderImage(300, 200, 'Image not available');
      case 'video':
        return getPlaceholderImage(300, 200, 'Video not available');
      case 'document':
        return getPlaceholderImage(300, 200, 'Document not available');
      default:
        return getPlaceholderImage(300, 200, 'File not available');
    }
  };

  // Render error state
  if (hasError || !currentUrl) {
    return (
      <div className={`universal-file-display error ${className}`} style={style}>
        {displayType === 'image' ? (
          <img 
            src={getPlaceholder()} 
            alt={alt}
            className="fallback-image"
            {...props}
          />
        ) : (
          <div className="error-placeholder">
            <span>File not available</span>
          </div>
        )}
      </div>
    );
  }

  // Render loading state
  if (isLoading) {
    return (
      <div className={`universal-file-display loading ${className}`} style={style}>
        <div className="loading-placeholder">
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  // Render based on display type
  switch (displayType) {
    case 'image':
      return (
        <img
          src={currentUrl}
          alt={alt}
          className={`universal-file-display image ${className}`}
          style={style}
          onError={handleError}
          onLoad={handleLoad}
          {...props}
        />
      );

    case 'video':
      return (
        <video
          src={currentUrl}
          className={`universal-file-display video ${className}`}
          style={style}
          onError={handleError}
          onLoadedData={handleLoad}
          controls
          {...props}
        />
      );

    case 'document':
      return (
        <iframe
          src={currentUrl}
          className={`universal-file-display document ${className}`}
          style={style}
          onError={handleError}
          onLoad={handleLoad}
          title={alt}
          {...props}
        />
      );

    default:
      return (
        <div className={`universal-file-display unknown ${className}`} style={style}>
          <a 
            href={currentUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="file-link"
          >
            {alt || 'Download File'}
          </a>
        </div>
      );
  }
};

export default UniversalFileDisplay;
