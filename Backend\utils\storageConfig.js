/**
 * Storage Configuration Utility
 * Centralized configuration for file storage decisions
 */

const { hasAWSCredentials } = require('./storageHelper');

/**
 * Storage strategy configuration
 */
const STORAGE_STRATEGY = {
  // Files that should always use local storage
  FORCE_LOCAL: [
    'profileImage', // Profile images always local for faster access
  ],
  
  // Files that should prefer S3 when available
  PREFER_S3: [
    'content', // Main content files
    'video',   // Video files
    'document', // Document files
  ],
  
  // Files that can use either storage
  FLEXIBLE: [
    'thumbnail', // Thumbnails can be either
    'preview',   // Preview files can be either
  ]
};

/**
 * Determine storage type for a given file field
 * @param {string} fieldName - The field name from the upload
 * @param {Object} options - Additional options
 * @returns {string} - 'local' or 's3'
 */
const determineStorageType = (fieldName, options = {}) => {
  const { forceLocal = false, forceS3 = false } = options;
  
  // Override options take precedence
  if (forceLocal) return 'local';
  if (forceS3 && hasAWSCredentials()) return 's3';
  
  // Check if field should be forced to local
  if (STORAGE_STRATEGY.FORCE_LOCAL.includes(fieldName)) {
    return 'local';
  }
  
  // If S3 is not configured, use local
  if (!hasAWSCredentials()) {
    return 'local';
  }
  
  // Check if field prefers S3
  if (STORAGE_STRATEGY.PREFER_S3.includes(fieldName)) {
    return 's3';
  }
  
  // Default to S3 if available, otherwise local
  return hasAWSCredentials() ? 's3' : 'local';
};

/**
 * Get storage configuration for the current environment
 * @returns {Object} - Storage configuration object
 */
const getStorageConfig = () => {
  const hasS3 = hasAWSCredentials();
  
  return {
    hasS3,
    defaultStorage: hasS3 ? 's3' : 'local',
    supportedStorageTypes: hasS3 ? ['local', 's3'] : ['local'],
    strategy: STORAGE_STRATEGY,
    
    // Helper methods
    shouldUseS3: (fieldName, options = {}) => {
      return determineStorageType(fieldName, options) === 's3';
    },
    
    shouldUseLocal: (fieldName, options = {}) => {
      return determineStorageType(fieldName, options) === 'local';
    }
  };
};

/**
 * Validate storage configuration
 * @returns {Object} - Validation result
 */
const validateStorageConfig = () => {
  const hasS3 = hasAWSCredentials();
  const errors = [];
  const warnings = [];
  
  if (!hasS3) {
    warnings.push('S3 credentials not configured - using local storage only');
  }
  
  // Check if uploads directory exists for local storage
  const fs = require('fs');
  const path = require('path');
  const uploadsDir = path.join(process.cwd(), 'uploads');
  
  if (!fs.existsSync(uploadsDir)) {
    try {
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log('[Storage] Created uploads directory:', uploadsDir);
    } catch (error) {
      errors.push(`Cannot create uploads directory: ${error.message}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    config: getStorageConfig()
  };
};

/**
 * Log storage configuration on startup
 */
const logStorageConfig = () => {
  const validation = validateStorageConfig();
  const config = validation.config;
  
  console.log('\n=== Storage Configuration ===');
  console.log(`Default Storage: ${config.defaultStorage.toUpperCase()}`);
  console.log(`Supported Types: ${config.supportedStorageTypes.join(', ').toUpperCase()}`);
  console.log(`S3 Available: ${config.hasS3 ? 'YES' : 'NO'}`);
  
  if (validation.warnings.length > 0) {
    console.log('\nWarnings:');
    validation.warnings.forEach(warning => console.log(`  - ${warning}`));
  }
  
  if (validation.errors.length > 0) {
    console.log('\nErrors:');
    validation.errors.forEach(error => console.log(`  - ${error}`));
  }
  
  console.log('============================\n');
  
  return validation;
};

module.exports = {
  STORAGE_STRATEGY,
  determineStorageType,
  getStorageConfig,
  validateStorageConfig,
  logStorageConfig
};
